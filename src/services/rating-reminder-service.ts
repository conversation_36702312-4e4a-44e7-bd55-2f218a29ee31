import { Preferences } from "@capacitor/preferences";
import { InAppBrowser } from "@capgo/inappbrowser";

// TypeScript Interfaces for Rating Reminder Configuration
export interface RatingReminderConfig {
  enabled: boolean;
  threshold: number; // Number of app opens before showing reminder
  frequency: number; // Days between reminders after "Maybe Later"
  position: 'center' | 'top' | 'bottom';
  zIndex: number;
}

export interface RatingReminderState {
  appOpenCount: number;
  lastReminderTime: number;
  userChoice: 'none' | 'rate-now' | 'maybe-later' | 'dont-ask-again';
  lastChoiceTime: number;
  reminderShownCount: number;
}

/**
 * RatingReminderService
 * 
 * Provides configurable app rating reminder functionality for web container applications.
 * Tracks app opens and displays rating prompts based on configurable thresholds and user preferences.
 * Uses the same injection mechanism as the DebugToolbarService for consistency.
 */
export class RatingReminderService {
  private static readonly DEFAULT_CONFIG: RatingReminderConfig = {
    enabled: import.meta.env.VITE_RATING_REMINDER_ENABLED === 'true',
    threshold: parseInt(import.meta.env.VITE_RATING_REMINDER_THRESHOLD) || 10,
    frequency: parseInt(import.meta.env.VITE_RATING_REMINDER_FREQUENCY) || 7, // 7 days
    position: 'center',
    zIndex: 999998 // One less than debug toolbar to avoid conflicts
  };

  private static readonly STORAGE_KEYS = {
    STATE: 'rating_reminder_state',
    CONFIG: 'rating_reminder_config'
  };

  private static readonly DEFAULT_STATE: RatingReminderState = {
    appOpenCount: 0,
    lastReminderTime: 0,
    userChoice: 'none',
    lastChoiceTime: 0,
    reminderShownCount: 0
  };

  private config: RatingReminderConfig = RatingReminderService.DEFAULT_CONFIG;
  private state: RatingReminderState = RatingReminderService.DEFAULT_STATE;
  private isInjected = false;
  private messageListeners: any[] = [];
  private readonly requestTimeout = 10000; // 10 seconds
  private readonly stablePageDelay = 5000; // 5 seconds
  private readonly minReinjectionInterval = 2000; // 2 seconds between re-injections
  private lastInjectionTime = 0;

  constructor() {
    console.log('🔍 [RatingReminder] Constructor called');
    console.log('🔍 [RatingReminder] Environment variables:', {
      VITE_RATING_REMINDER_ENABLED: import.meta.env.VITE_RATING_REMINDER_ENABLED,
      VITE_RATING_REMINDER_THRESHOLD: import.meta.env.VITE_RATING_REMINDER_THRESHOLD,
      VITE_RATING_REMINDER_FREQUENCY: import.meta.env.VITE_RATING_REMINDER_FREQUENCY
    });
    console.log('🔍 [RatingReminder] Default config created:', this.config);
  }

  /**
   * Initialize the service and load persisted state
   */
  async initialize(): Promise<void> {
    console.log('🔍 [RatingReminder] Initializing service...');

    try {
      console.log('🔍 [RatingReminder] Loading configuration...');
      await this.loadConfig();

      console.log('🔍 [RatingReminder] Loading state...');
      await this.loadState();

      console.log('✅ [RatingReminder] Service initialized successfully:', {
        enabled: this.config.enabled,
        threshold: this.config.threshold,
        frequency: this.config.frequency,
        currentCount: this.state.appOpenCount,
        userChoice: this.state.userChoice,
        reminderShownCount: this.state.reminderShownCount
      });

      // Log detailed status for debugging
      this.logStatus();

    } catch (error) {
      console.error('❌ [RatingReminder] Failed to initialize service:', error);
    }
  }

  /**
   * Check if rating reminder is enabled
   */
  isEnabled(): boolean {
    return this.config.enabled;
  }

  /**
   * Get current service status
   */
  getStatus(): any {
    const now = Date.now();
    const daysSinceLastChoice = this.state.lastChoiceTime > 0
      ? (now - this.state.lastChoiceTime) / (1000 * 60 * 60 * 24)
      : 0;
    const daysSinceLastReminder = this.state.lastReminderTime > 0
      ? (now - this.state.lastReminderTime) / (1000 * 60 * 60 * 24)
      : 0;

    return {
      enabled: this.config.enabled,
      injected: this.isInjected,
      config: { ...this.config },
      state: { ...this.state },
      shouldShow: this.shouldShowReminder(),
      debug: {
        thresholdMet: this.state.appOpenCount >= this.config.threshold,
        userBlocked: this.state.userChoice === 'dont-ask-again',
        waitingForFrequency: this.state.userChoice === 'maybe-later' && daysSinceLastChoice < this.config.frequency,
        daysSinceLastChoice: Math.round(daysSinceLastChoice * 100) / 100,
        daysSinceLastReminder: Math.round(daysSinceLastReminder * 100) / 100,
        nextEligibleDate: this.getNextEligibleDate(),
        canInject: this.canInjectReminder()
      }
    };
  }

  /**
   * Get detailed debug information
   */
  getDebugInfo(): any {
    const now = Date.now();
    return {
      service: 'RatingReminderService',
      version: '1.0.0',
      initialized: true,
      timestamp: now,
      config: {
        ...this.config,
        source: 'environment_variables'
      },
      state: {
        ...this.state,
        appOpenCountProgress: `${this.state.appOpenCount}/${this.config.threshold}`,
        lastReminderTimeFormatted: this.state.lastReminderTime > 0
          ? new Date(this.state.lastReminderTime).toISOString()
          : 'never',
        lastChoiceTimeFormatted: this.state.lastChoiceTime > 0
          ? new Date(this.state.lastChoiceTime).toISOString()
          : 'never'
      },
      conditions: {
        enabled: this.config.enabled,
        thresholdReached: this.state.appOpenCount >= this.config.threshold,
        notPermanentlyDismissed: this.state.userChoice !== 'dont-ask-again',
        frequencyRespected: this.isFrequencyRespected(),
        notCurrentlyInjected: !this.isInjected,
        canShow: this.shouldShowReminder()
      },
      injection: {
        isInjected: this.isInjected,
        lastInjectionTime: this.lastInjectionTime,
        minReinjectionInterval: this.minReinjectionInterval,
        stablePageDelay: this.stablePageDelay
      }
    };
  }

  /**
   * Get next eligible date for showing reminder
   */
  private getNextEligibleDate(): string | null {
    if (this.state.userChoice === 'dont-ask-again') {
      return 'never (permanently dismissed)';
    }

    if (this.state.appOpenCount < this.config.threshold) {
      const remainingOpens = this.config.threshold - this.state.appOpenCount;
      return `after ${remainingOpens} more app opens`;
    }

    if (this.state.userChoice === 'maybe-later' && this.state.lastChoiceTime > 0) {
      const nextDate = new Date(this.state.lastChoiceTime + (this.config.frequency * 24 * 60 * 60 * 1000));
      return nextDate.toISOString();
    }

    return 'now (conditions met)';
  }

  /**
   * Check if frequency requirement is respected
   */
  private isFrequencyRespected(): boolean {
    if (this.state.userChoice !== 'maybe-later' || this.state.lastChoiceTime === 0) {
      return true;
    }

    const daysSinceLastChoice = (Date.now() - this.state.lastChoiceTime) / (1000 * 60 * 60 * 24);
    return daysSinceLastChoice >= this.config.frequency;
  }

  /**
   * Check if reminder can be injected (all technical conditions met)
   */
  private canInjectReminder(): boolean {
    const now = Date.now();
    const canInject = !this.isInjected &&
                     (now - this.lastInjectionTime) >= this.minReinjectionInterval;
    return canInject;
  }

  /**
   * Log current status to console (for debugging)
   */
  logStatus(): void {
    const status = this.getStatus();
    console.group('🌟 Rating Reminder Service Status');
    console.log('Enabled:', status.enabled);
    console.log('Should Show:', status.shouldShow);
    console.log('App Opens:', `${status.state.appOpenCount}/${status.config.threshold}`);
    console.log('User Choice:', status.state.userChoice);
    console.log('Injected:', status.injected);

    if (status.debug.nextEligibleDate) {
      console.log('Next Eligible:', status.debug.nextEligibleDate);
    }

    if (!status.shouldShow) {
      const reasons = [];
      if (!status.enabled) reasons.push('disabled');
      if (!status.debug.thresholdMet) reasons.push('threshold not met');
      if (status.debug.userBlocked) reasons.push('user chose "don\'t ask again"');
      if (status.debug.waitingForFrequency) reasons.push('waiting for frequency interval');
      if (status.injected) reasons.push('already injected');

      console.log('Blocked by:', reasons.join(', '));
    }

    console.groupEnd();
  }

  /**
   * Validate service configuration and state
   */
  validateConfiguration(): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Validate config
    if (this.config.threshold < 1) {
      errors.push('Threshold must be at least 1');
    }
    if (this.config.frequency < 1) {
      errors.push('Frequency must be at least 1 day');
    }
    if (this.config.zIndex < 1) {
      errors.push('Z-index must be positive');
    }

    // Validate state
    if (this.state.appOpenCount < 0) {
      errors.push('App open count cannot be negative');
    }
    if (this.state.lastReminderTime < 0) {
      errors.push('Last reminder time cannot be negative');
    }
    if (this.state.lastChoiceTime < 0) {
      errors.push('Last choice time cannot be negative');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Track an app open event
   */
  async trackAppOpen(): Promise<void> {
    console.log('🔍 [RatingReminder] trackAppOpen() called');

    if (!this.config.enabled) {
      console.log('🔍 [RatingReminder] Service disabled, skipping app open tracking');
      return;
    }

    try {
      const previousCount = this.state.appOpenCount;
      this.state.appOpenCount++;

      console.log(`🔍 [RatingReminder] Incrementing app open count: ${previousCount} -> ${this.state.appOpenCount}`);

      // Save state with retry logic
      const saveSuccess = await this.saveStateWithRetry();
      if (!saveSuccess) {
        console.warn('🔍 [RatingReminder] Failed to save app open count, reverting increment');
        this.state.appOpenCount = previousCount;
        return;
      }

      console.log(`🔍 [RatingReminder] App open tracked successfully. Count: ${this.state.appOpenCount}/${this.config.threshold} (was ${previousCount})`);

      // Log milestone achievements
      if (this.state.appOpenCount === this.config.threshold) {
        console.log('🎉 [RatingReminder] Threshold reached for the first time!');
      }

      // Debug current state before checking conditions
      const debugInfo = this.getDebugInfo();
      console.log('🔍 [RatingReminder] Current state before condition check:', {
        appOpenCount: this.state.appOpenCount,
        threshold: this.config.threshold,
        userChoice: this.state.userChoice,
        isInjected: this.isInjected,
        enabled: this.config.enabled
      });

      // Check if we should show the reminder
      const shouldShow = this.shouldShowReminder();
      console.log(`🔍 [RatingReminder] shouldShowReminder() returned: ${shouldShow}`);

      if (shouldShow) {
        console.log('🎯 [RatingReminder] All conditions met! Scheduling display...');
        // Use smart scheduling with page stability detection
        await this.scheduleReminderDisplay();
      } else {
        const conditions = this.evaluateDisplayConditions();
        console.log('❌ [RatingReminder] Conditions not met. Blocking reasons:', conditions.blockingReasons);
        console.log('🔍 [RatingReminder] Detailed conditions:', {
          enabled: conditions.enabled,
          thresholdMet: conditions.thresholdMet,
          notPermanentlyDismissed: conditions.notPermanentlyDismissed,
          frequencyRespected: conditions.frequencyRespected,
          notCurrentlyInjected: conditions.notCurrentlyInjected
        });
      }
    } catch (error) {
      console.error('🔍 [RatingReminder] Failed to track app open:', error);
    }
  }

  /**
   * Track app resume event (different from fresh open)
   */
  async trackAppResume(): Promise<void> {
    if (!this.config.enabled) {
      return;
    }

    try {
      console.log('App resume detected');

      // For resume events, we might want different logic
      // For now, treat it the same as app open, but we could add
      // different thresholds or behaviors in the future
      await this.trackAppOpen();
    } catch (error) {
      console.error('Failed to track app resume:', error);
    }
  }

  /**
   * Manually increment app open count (for testing or special cases)
   */
  async incrementAppOpenCount(amount: number = 1): Promise<boolean> {
    if (!this.config.enabled || amount < 1) {
      return false;
    }

    try {
      const previousCount = this.state.appOpenCount;
      this.state.appOpenCount += amount;

      const saveSuccess = await this.saveStateWithRetry();
      if (!saveSuccess) {
        this.state.appOpenCount = previousCount;
        return false;
      }

      console.log(`App open count manually incremented by ${amount}. New count: ${this.state.appOpenCount}`);
      return true;
    } catch (error) {
      console.error('Failed to increment app open count:', error);
      return false;
    }
  }

  /**
   * Get app open statistics
   */
  getAppOpenStats(): any {
    const progress = this.config.threshold > 0
      ? Math.min(100, (this.state.appOpenCount / this.config.threshold) * 100)
      : 0;

    return {
      currentCount: this.state.appOpenCount,
      threshold: this.config.threshold,
      remaining: Math.max(0, this.config.threshold - this.state.appOpenCount),
      progress: Math.round(progress * 100) / 100,
      thresholdReached: this.state.appOpenCount >= this.config.threshold,
      reminderShownCount: this.state.reminderShownCount
    };
  }

  /**
   * Check if reminder should be shown based on current state
   */
  private shouldShowReminder(): boolean {
    // Quick checks first
    if (!this.config.enabled) {
      return false;
    }

    // Check if popup is actually present in DOM before trusting isInjected flag
    if (this.isInjected) {
      this.verifyInjectionState();
    }

    if (this.isInjected) {
      return false;
    }

    // Check all conditions systematically
    const conditions = this.evaluateDisplayConditions();

    return conditions.enabled &&
           conditions.thresholdMet &&
           conditions.notPermanentlyDismissed &&
           conditions.frequencyRespected &&
           conditions.notCurrentlyInjected;
  }

  /**
   * Evaluate all display conditions and return detailed results
   */
  private evaluateDisplayConditions(): {
    enabled: boolean;
    thresholdMet: boolean;
    notPermanentlyDismissed: boolean;
    frequencyRespected: boolean;
    notCurrentlyInjected: boolean;
    canShow: boolean;
    blockingReasons: string[];
  } {
    const conditions = {
      enabled: this.config.enabled,
      thresholdMet: this.state.appOpenCount >= this.config.threshold,
      notPermanentlyDismissed: this.state.userChoice !== 'dont-ask-again',
      frequencyRespected: this.isFrequencyRespected(),
      notCurrentlyInjected: !this.isInjected,
      canShow: false,
      blockingReasons: [] as string[]
    };

    // Determine blocking reasons
    if (!conditions.enabled) {
      conditions.blockingReasons.push('feature disabled');
    }
    if (!conditions.thresholdMet) {
      const remaining = this.config.threshold - this.state.appOpenCount;
      conditions.blockingReasons.push(`threshold not met (${remaining} more opens needed)`);
    }
    if (!conditions.notPermanentlyDismissed) {
      conditions.blockingReasons.push('user chose "don\'t ask again"');
    }
    if (!conditions.frequencyRespected) {
      const nextDate = this.getNextEligibleDate();
      conditions.blockingReasons.push(`frequency not met (next eligible: ${nextDate})`);
    }
    if (!conditions.notCurrentlyInjected) {
      conditions.blockingReasons.push('reminder already displayed');
    }

    conditions.canShow = conditions.enabled &&
                        conditions.thresholdMet &&
                        conditions.notPermanentlyDismissed &&
                        conditions.frequencyRespected &&
                        conditions.notCurrentlyInjected;

    return conditions;
  }

  /**
   * Check if the app open threshold has been reached
   */
  isThresholdReached(): boolean {
    return this.state.appOpenCount >= this.config.threshold;
  }

  /**
   * Check if user has permanently dismissed the reminder
   */
  isPermanentlyDismissed(): boolean {
    return this.state.userChoice === 'dont-ask-again';
  }

  /**
   * Get the number of app opens remaining until threshold
   */
  getOpensUntilThreshold(): number {
    return Math.max(0, this.config.threshold - this.state.appOpenCount);
  }

  /**
   * Get the number of days until next reminder is eligible (for "Maybe Later")
   */
  getDaysUntilNextReminder(): number {
    if (this.state.userChoice !== 'maybe-later' || this.state.lastChoiceTime === 0) {
      return 0;
    }

    const daysSinceLastChoice = (Date.now() - this.state.lastChoiceTime) / (1000 * 60 * 60 * 24);
    return Math.max(0, this.config.frequency - daysSinceLastChoice);
  }

  /**
   * Check if all technical conditions are met for injection
   */
  canInjectNow(): boolean {
    const now = Date.now();
    const technicalConditions = {
      notInjected: !this.isInjected,
      debounceRespected: (now - this.lastInjectionTime) >= this.minReinjectionInterval,
      serviceEnabled: this.config.enabled
    };

    return technicalConditions.notInjected &&
           technicalConditions.debounceRespected &&
           technicalConditions.serviceEnabled;
  }

  /**
   * Get comprehensive threshold analysis
   */
  getThresholdAnalysis(): any {
    const conditions = this.evaluateDisplayConditions();

    return {
      current: this.state.appOpenCount,
      threshold: this.config.threshold,
      remaining: this.getOpensUntilThreshold(),
      progress: this.getAppOpenStats().progress,
      thresholdReached: conditions.thresholdMet,
      canShow: conditions.canShow,
      blockingReasons: conditions.blockingReasons,
      nextEligibleDate: this.getNextEligibleDate(),
      daysUntilNextReminder: this.getDaysUntilNextReminder()
    };
  }

  /**
   * Show reminder if all conditions are met and page is stable
   */
  private async showReminderIfReady(): Promise<void> {
    console.log('🔍 [RatingReminder] showReminderIfReady() called');

    const shouldShow = this.shouldShowReminder();
    console.log(`🔍 [RatingReminder] shouldShowReminder() check in showReminderIfReady: ${shouldShow}`);

    if (!shouldShow) {
      const conditions = this.evaluateDisplayConditions();
      console.log('🔍 [RatingReminder] Conditions not met in showReminderIfReady, blocking reasons:', conditions.blockingReasons);
      return;
    }

    // Debounce injection attempts
    const now = Date.now();
    const timeSinceLastInjection = now - this.lastInjectionTime;
    console.log(`🔍 [RatingReminder] Time since last injection: ${timeSinceLastInjection}ms (min required: ${this.minReinjectionInterval}ms)`);

    if (timeSinceLastInjection < this.minReinjectionInterval) {
      console.log('🔍 [RatingReminder] Injection debounced, too soon since last attempt');
      return;
    }

    try {
      // Check if page is stable before injecting
      console.log('🔍 [RatingReminder] Checking page stability...');
      const isPageStable = await this.checkPageStability();
      console.log(`🔍 [RatingReminder] Page stability check result: ${isPageStable}`);

      if (!isPageStable) {
        console.log('🔍 [RatingReminder] Page not stable, scheduling retry in 2 seconds...');
        // Retry after additional delay
        setTimeout(async () => {
          console.log('🔍 [RatingReminder] Retrying after page stability delay...');
          await this.showReminderIfReady();
        }, 2000); // Additional 2-second delay
        return;
      }

      console.log('🎯 [RatingReminder] Page is stable, attempting injection...');
      const success = await this.injectReminder();
      console.log(`🔍 [RatingReminder] Injection result: ${success}`);

      if (success) {
        this.lastInjectionTime = now;
        this.state.lastReminderTime = now;
        this.state.reminderShownCount++;
        await this.saveState();
        console.log('✅ [RatingReminder] Rating reminder displayed successfully!');
      } else {
        console.log('❌ [RatingReminder] Injection failed');
      }
    } catch (error) {
      console.error('🔍 [RatingReminder] Failed to show rating reminder:', error);
    }
  }

  /**
   * Check if the page is stable and ready for popup injection
   */
  private async checkPageStability(): Promise<boolean> {
    console.log('🔍 [RatingReminder] checkPageStability() called');

    try {
      // Check if we can execute a simple script to test page readiness
      const testScript = `
        (function() {
          console.log('🔍 [RatingReminder] Page stability check script executing...');

          // Check if document is ready
          if (document.readyState !== 'complete') {
            console.log('🔍 [RatingReminder] Document not ready:', document.readyState);
            return false;
          }

          // Check if page is still loading
          if (document.querySelector('body') === null) {
            console.log('🔍 [RatingReminder] Body element not found');
            return false;
          }

          // Check for common loading indicators
          const loadingSelectors = [
            '[class*="loading"]',
            '[class*="spinner"]',
            '[id*="loading"]',
            '[id*="spinner"]',
            '.loader',
            '#loader'
          ];

          let foundLoadingElement = false;
          for (const selector of loadingSelectors) {
            const elements = document.querySelectorAll(selector);
            for (const element of elements) {
              if (element.offsetParent !== null) { // Element is visible
                console.log('🔍 [RatingReminder] Found visible loading element:', selector);
                foundLoadingElement = true;
                break;
              }
            }
            if (foundLoadingElement) break;
          }

          if (foundLoadingElement) {
            return false;
          }

          console.log('🔍 [RatingReminder] Page stability check passed');
          return true;
        })();
      `;

      console.log('🔍 [RatingReminder] Executing page stability check script...');
      await InAppBrowser.executeScript({ code: testScript });

      // If script execution succeeds, assume page is stable
      console.log('🔍 [RatingReminder] Page stability check script executed successfully');
      return true;
    } catch (error) {
      console.warn('🔍 [RatingReminder] Failed to check page stability, assuming stable:', error);
      return true; // Fail-safe: assume stable if we can't check
    }
  }

  /**
   * Schedule reminder display with smart timing
   */
  async scheduleReminderDisplay(): Promise<void> {
    console.log('🔍 [RatingReminder] scheduleReminderDisplay() called');

    if (!this.shouldShowReminder()) {
      console.log('🔍 [RatingReminder] shouldShowReminder() returned false in scheduleReminderDisplay, aborting');
      return;
    }

    console.log(`🔍 [RatingReminder] Scheduling rating reminder display with ${this.stablePageDelay}ms delay...`);

    // Initial 5-second delay for page stability
    setTimeout(async () => {
      console.log('🔍 [RatingReminder] 5-second delay completed, calling showReminderIfReady()');
      await this.showReminderIfReady();
    }, this.stablePageDelay);

    console.log('🔍 [RatingReminder] setTimeout scheduled successfully');
  }

  /**
   * Force display reminder (for testing purposes)
   */
  async forceDisplayReminder(): Promise<boolean> {
    console.log('🔍 [RatingReminder] Force displaying rating reminder (bypassing conditions)...');

    try {
      // First reset injection state
      this.isInjected = false;
      this.lastInjectionTime = 0;

      // Remove any existing popup
      await this.removeReminder();

      // Force inject
      const success = await this.injectReminder();
      if (success) {
        this.lastInjectionTime = Date.now();
        this.state.lastReminderTime = Date.now();
        this.state.reminderShownCount++;
        await this.saveState();
        console.log('✅ [RatingReminder] Force display successful');
      } else {
        console.log('❌ [RatingReminder] Force display failed');
      }
      return success;
    } catch (error) {
      console.error('❌ [RatingReminder] Failed to force display rating reminder:', error);
      return false;
    }
  }

  /**
   * Check if page is currently in a loading state
   */
  private async isPageLoading(): Promise<boolean> {
    try {
      const checkScript = `
        (function() {
          return document.readyState !== 'complete' ||
                 document.querySelector('body') === null ||
                 window.location.href === 'about:blank';
        })();
      `;

      await InAppBrowser.executeScript({ code: checkScript });
      return false; // If script executes successfully, page is not loading
    } catch (error) {
      console.warn('Failed to check page loading state:', error);
      return false; // Assume not loading if we can't check
    }
  }

  /**
   * Inject rating reminder popup into web container
   */
  async injectReminder(): Promise<boolean> {
    console.log('🔍 [RatingReminder] injectReminder() called');

    if (!this.config.enabled) {
      console.log('🔍 [RatingReminder] Service not enabled, aborting injection');
      return false;
    }

    if (this.isInjected) {
      console.log('🔍 [RatingReminder] Already injected, aborting injection');
      return false;
    }

    if (!this.shouldShowReminder()) {
      console.log('🔍 [RatingReminder] shouldShowReminder() returned false in injectReminder');
      return false;
    }

    try {
      console.log('🔍 [RatingReminder] Starting injection process...');

      // Generate reminder HTML and CSS
      console.log('🔍 [RatingReminder] Generating reminder script...');
      const reminderScript = this.generateReminderScript();
      console.log('🔍 [RatingReminder] Reminder script generated, length:', reminderScript.length);

      // Inject the reminder script
      console.log('🔍 [RatingReminder] Executing script injection...');
      await InAppBrowser.executeScript({ code: reminderScript });
      console.log('🔍 [RatingReminder] Script injection completed');

      // Set up message listeners for reminder communication
      console.log('🔍 [RatingReminder] Setting up message listeners...');
      await this.setupMessageListeners();
      console.log('🔍 [RatingReminder] Message listeners setup completed');

      this.isInjected = true;
      console.log('✅ [RatingReminder] Rating reminder injected successfully!');
      return true;

    } catch (error) {
      console.error('❌ [RatingReminder] Failed to inject rating reminder:', error);
      return false;
    }
  }

  /**
   * Remove rating reminder and cleanup
   */
  async removeReminder(): Promise<void> {
    console.log('🔍 [RatingReminder] removeReminder() called, isInjected:', this.isInjected);

    try {
      // Remove reminder from DOM (try even if isInjected is false, in case of state mismatch)
      const removeScript = `
        (function() {
          console.log('🔍 [RatingReminder] Removal script executing...');

          // Remove both overlay and container
          const overlay = document.getElementById('rating-reminder-overlay');
          const container = document.getElementById('rating-reminder-container');

          if (overlay) {
            console.log('🔍 [RatingReminder] Removing overlay');
            overlay.remove();
          }

          if (container) {
            console.log('🔍 [RatingReminder] Removing container');
            container.remove();
          }

          // Clean up global references
          if (window._ratingReminderInjected) {
            console.log('🔍 [RatingReminder] Cleaning up global reference');
            delete window._ratingReminderInjected;
          }

          console.log('🔍 [RatingReminder] Removal script complete');
        })();
      `;

      console.log('🔍 [RatingReminder] Executing removal script...');
      await InAppBrowser.executeScript({ code: removeScript });

      // Clean up message listeners
      this.cleanupListeners();

      this.isInjected = false;
      console.log('✅ [RatingReminder] Rating reminder removed successfully');

    } catch (error) {
      console.error('❌ [RatingReminder] Failed to remove rating reminder:', error);
      // Reset state anyway
      this.isInjected = false;
    }
  }

  /**
   * Load configuration from storage or use defaults
   */
  private async loadConfig(): Promise<void> {
    try {
      const result = await Preferences.get({ key: RatingReminderService.STORAGE_KEYS.CONFIG });
      if (result.value) {
        const storedConfig = JSON.parse(result.value);
        this.config = { ...RatingReminderService.DEFAULT_CONFIG, ...storedConfig };
      } else {
        this.config = RatingReminderService.DEFAULT_CONFIG;
      }
    } catch (error) {
      console.warn('Failed to load rating reminder config, using defaults:', error);
      this.config = RatingReminderService.DEFAULT_CONFIG;
    }
  }

  /**
   * Load state from storage or use defaults
   */
  private async loadState(): Promise<void> {
    try {
      const result = await Preferences.get({ key: RatingReminderService.STORAGE_KEYS.STATE });
      if (result.value) {
        const storedState = JSON.parse(result.value);
        this.state = { ...RatingReminderService.DEFAULT_STATE, ...storedState };
      } else {
        this.state = RatingReminderService.DEFAULT_STATE;
      }
    } catch (error) {
      console.warn('Failed to load rating reminder state, using defaults:', error);
      this.state = RatingReminderService.DEFAULT_STATE;
    }
  }

  /**
   * Save current state to storage
   */
  private async saveState(): Promise<void> {
    try {
      await Preferences.set({
        key: RatingReminderService.STORAGE_KEYS.STATE,
        value: JSON.stringify(this.state)
      });
    } catch (error) {
      console.error('Failed to save rating reminder state:', error);
      throw error;
    }
  }

  /**
   * Save state with retry logic for critical operations
   */
  private async saveStateWithRetry(maxRetries: number = 3): Promise<boolean> {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        await this.saveState();
        return true;
      } catch (error) {
        console.warn(`Failed to save state (attempt ${attempt}/${maxRetries}):`, error);

        if (attempt === maxRetries) {
          console.error('All save attempts failed, state may be inconsistent');
          return false;
        }

        // Wait before retry (exponential backoff)
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 100));
      }
    }
    return false;
  }

  /**
   * Save current configuration to storage
   */
  private async saveConfig(): Promise<void> {
    try {
      await Preferences.set({
        key: RatingReminderService.STORAGE_KEYS.CONFIG,
        value: JSON.stringify(this.config)
      });
    } catch (error) {
      console.error('Failed to save rating reminder config:', error);
    }
  }

  /**
   * Update configuration with new values
   */
  async updateConfig(newConfig: Partial<RatingReminderConfig>): Promise<void> {
    try {
      this.config = { ...this.config, ...newConfig };
      await this.saveConfig();
      console.log('Rating reminder configuration updated:', this.config);
    } catch (error) {
      console.error('Failed to update rating reminder config:', error);
    }
  }

  /**
   * Reset configuration to defaults
   */
  async resetConfig(): Promise<void> {
    try {
      this.config = RatingReminderService.DEFAULT_CONFIG;
      await this.saveConfig();
      console.log('Rating reminder configuration reset to defaults');
    } catch (error) {
      console.error('Failed to reset rating reminder config:', error);
    }
  }

  /**
   * Reset state to defaults (useful for testing or user reset)
   */
  async resetState(): Promise<void> {
    try {
      this.state = RatingReminderService.DEFAULT_STATE;
      await this.saveState();
      console.log('Rating reminder state reset to defaults');
    } catch (error) {
      console.error('Failed to reset rating reminder state:', error);
    }
  }

  /**
   * Get configuration (read-only)
   */
  getConfig(): Readonly<RatingReminderConfig> {
    return { ...this.config };
  }

  /**
   * Get current state (read-only)
   */
  getState(): Readonly<RatingReminderState> {
    return { ...this.state };
  }

  /**
   * Check if reminder is currently injected
   */
  isReminderInjected(): boolean {
    return this.isInjected;
  }

  /**
   * Handle user choice: "Rate Now"
   */
  async handleRateNow(): Promise<void> {
    try {
      console.log('User chose: Rate Now');

      this.state.userChoice = 'rate-now';
      this.state.lastChoiceTime = Date.now();

      await this.saveStateWithRetry();
      await this.removeReminder();

      // TODO: Open app store rating page (platform-specific implementation)
      console.log('TODO: Open app store rating page');

    } catch (error) {
      console.error('Failed to handle "Rate Now" choice:', error);
    }
  }

  /**
   * Handle user choice: "Maybe Later"
   */
  async handleMaybeLater(): Promise<void> {
    try {
      console.log('User chose: Maybe Later');

      this.state.userChoice = 'maybe-later';
      this.state.lastChoiceTime = Date.now();

      await this.saveStateWithRetry();
      await this.removeReminder();

      const nextReminderDate = new Date(Date.now() + (this.config.frequency * 24 * 60 * 60 * 1000));
      console.log(`Next reminder eligible on: ${nextReminderDate.toLocaleDateString()}`);

    } catch (error) {
      console.error('Failed to handle "Maybe Later" choice:', error);
    }
  }

  /**
   * Handle user choice: "Don't Ask Again"
   */
  async handleDontAskAgain(): Promise<void> {
    try {
      console.log('User chose: Don\'t Ask Again');

      this.state.userChoice = 'dont-ask-again';
      this.state.lastChoiceTime = Date.now();

      await this.saveStateWithRetry();
      await this.removeReminder();

      console.log('Rating reminder permanently disabled for this user');

    } catch (error) {
      console.error('Failed to handle "Don\'t Ask Again" choice:', error);
    }
  }

  /**
   * Handle user dismissing the reminder without choosing an option
   */
  async handleDismiss(): Promise<void> {
    try {
      console.log('User dismissed reminder without choosing');

      // Just remove the reminder, don't change user choice
      await this.removeReminder();

    } catch (error) {
      console.error('Failed to handle reminder dismissal:', error);
    }
  }

  /**
   * Reset user choice (useful for testing or user preference reset)
   */
  async resetUserChoice(): Promise<void> {
    try {
      console.log('Resetting user choice to none');

      this.state.userChoice = 'none';
      this.state.lastChoiceTime = 0;

      await this.saveStateWithRetry();

    } catch (error) {
      console.error('Failed to reset user choice:', error);
    }
  }

  /**
   * Get user choice history and statistics
   */
  getUserChoiceInfo(): any {
    const now = Date.now();
    const daysSinceLastChoice = this.state.lastChoiceTime > 0
      ? (now - this.state.lastChoiceTime) / (1000 * 60 * 60 * 24)
      : 0;

    return {
      currentChoice: this.state.userChoice,
      lastChoiceTime: this.state.lastChoiceTime,
      lastChoiceTimeFormatted: this.state.lastChoiceTime > 0
        ? new Date(this.state.lastChoiceTime).toISOString()
        : 'never',
      daysSinceLastChoice: Math.round(daysSinceLastChoice * 100) / 100,
      isPermanentlyDismissed: this.isPermanentlyDismissed(),
      isWaitingForFrequency: this.state.userChoice === 'maybe-later' &&
                            daysSinceLastChoice < this.config.frequency,
      daysUntilNextEligible: this.getDaysUntilNextReminder(),
      reminderShownCount: this.state.reminderShownCount
    };
  }

  /**
   * Check if user can change their choice (for UI purposes)
   */
  canUserChangeChoice(): boolean {
    // Users can always reset their choice, but we might want to add restrictions
    return true;
  }

  /**
   * Cleanup service and remove any injected content
   */
  async cleanup(): Promise<void> {
    try {
      if (this.isInjected) {
        await this.removeReminder();
      }
      this.cleanupListeners();
      console.log('Rating reminder service cleaned up successfully');
    } catch (error) {
      console.error('Failed to cleanup rating reminder service:', error);
    }
  }

  /**
   * Setup message listeners for reminder communication
   */
  private async setupMessageListeners(): Promise<void> {
    try {
      console.log('🔍 [RatingReminder] Setting up message listeners...');

      // Listen for messages from the injected popup
      const messageListener = async (event: any) => {
        try {
          // Handle both InAppBrowser and regular postMessage events
          const data = event.detail || event.data;

          console.log('🔍 [RatingReminder] Message received:', data);

          if (!data || data.type !== 'ratingReminder') {
            console.log('🔍 [RatingReminder] Message not for rating reminder, ignoring');
            return;
          }

          console.log('✅ [RatingReminder] Rating reminder message received:', data);

          // Handle different actions
          switch (data.action) {
            case 'popupReady':
              console.log('✅ [RatingReminder] Popup confirmed ready and visible');
              // Popup is confirmed to be injected and ready
              break;
            case 'rateNow':
              console.log('🔍 [RatingReminder] Handling rateNow action');
              await this.handleRateNow();
              break;
            case 'maybeLater':
              console.log('🔍 [RatingReminder] Handling maybeLater action');
              await this.handleMaybeLater();
              break;
            case 'dontAskAgain':
              console.log('🔍 [RatingReminder] Handling dontAskAgain action');
              await this.handleDontAskAgain();
              break;
            case 'dismiss':
              console.log('🔍 [RatingReminder] Handling dismiss action');
              await this.handleDismiss();
              break;
            default:
              console.warn('🔍 [RatingReminder] Unknown action:', data.action);
          }
        } catch (error) {
          console.error('🔍 [RatingReminder] Error handling message:', error);
        }
      };

      // Add listener for InAppBrowser messages (using the correct event name)
      console.log('🔍 [RatingReminder] Adding InAppBrowser listener...');
      const listenerHandle = await InAppBrowser.addListener('messageFromWebview', messageListener);
      this.messageListeners.push({ type: 'messageFromWebview', listener: listenerHandle });
      console.log('🔍 [RatingReminder] InAppBrowser listener added');

      // Also listen for regular window messages as fallback
      console.log('🔍 [RatingReminder] Adding window message listener...');
      const windowMessageListener = (event: MessageEvent) => {
        console.log('🔍 [RatingReminder] Window message received:', event);
        messageListener(event);
      };

      window.addEventListener('message', windowMessageListener);
      this.messageListeners.push({ type: 'message', listener: windowMessageListener });
      console.log('🔍 [RatingReminder] Window message listener added');

      console.log('✅ [RatingReminder] Message listeners setup complete, total listeners:', this.messageListeners.length);
    } catch (error) {
      console.error('❌ [RatingReminder] Failed to setup message listeners:', error);
    }
  }

  /**
   * Clean up message listeners
   */
  private cleanupListeners(): void {
    try {
      console.log('Cleaning up rating reminder message listeners...');

      this.messageListeners.forEach(({ type, listener }) => {
        try {
          if (type === 'messageFromWebview') {
            // Remove InAppBrowser listener handle
            if (listener && listener.remove) {
              listener.remove();
            }
          } else if (type === 'message') {
            // Remove window message listener
            window.removeEventListener('message', listener);
          }
        } catch (error) {
          console.warn('Error removing listener:', error);
        }
      });

      this.messageListeners = [];
      console.log('Rating reminder message listeners cleaned up');
    } catch (error) {
      console.error('Failed to cleanup rating reminder message listeners:', error);
    }
  }

  /**
   * Send a test message to verify communication (for debugging)
   */
  async sendTestMessage(): Promise<void> {
    try {
      await InAppBrowser.postMessage({
        detail: {
          type: 'ratingReminderTest',
          message: 'Test message from rating reminder service',
          timestamp: Date.now()
        }
      });
      console.log('Test message sent to rating reminder popup');
    } catch (error) {
      console.error('Failed to send test message:', error);
    }
  }

  /**
   * Check if message listeners are properly set up
   */
  areListenersActive(): boolean {
    return this.messageListeners.length > 0;
  }

  /**
   * Test method to quickly trigger rating reminder (for debugging)
   */
  async testRatingReminder(): Promise<void> {
    console.log('🔍 [RatingReminder] TEST MODE: Setting up for immediate display...');

    // Temporarily set threshold to 1 and reset user choice
    const originalThreshold = this.config.threshold;
    const originalUserChoice = this.state.userChoice;

    this.config.threshold = 1;
    this.state.userChoice = 'none';
    this.state.lastChoiceTime = 0;
    this.state.appOpenCount = 0;

    console.log('🔍 [RatingReminder] TEST MODE: Configuration adjusted for testing');

    // Track an app open to trigger the reminder
    await this.trackAppOpen();

    // Restore original values after a delay
    setTimeout(() => {
      this.config.threshold = originalThreshold;
      this.state.userChoice = originalUserChoice;
      console.log('🔍 [RatingReminder] TEST MODE: Original configuration restored');
    }, 10000); // 10 seconds
  }

  /**
   * Reset all state for testing purposes
   */
  async resetForTesting(): Promise<void> {
    console.log('🔍 [RatingReminder] Resetting state for testing...');

    this.state = {
      appOpenCount: 0,
      lastReminderTime: 0,
      userChoice: 'none',
      lastChoiceTime: 0,
      reminderShownCount: 0
    };

    this.isInjected = false;
    this.lastInjectionTime = 0;

    // Also remove any existing popup
    await this.removeReminder();

    await this.saveState();
    console.log('🔍 [RatingReminder] State reset complete');
  }

  /**
   * Verify if the popup is actually present in the DOM
   */
  private async verifyInjectionState(): Promise<void> {
    try {
      const checkScript = `
        (function() {
          const overlay = document.getElementById('rating-reminder-overlay');
          const isPresent = overlay !== null;
          console.log('🔍 [RatingReminder] DOM verification - popup present:', isPresent);
          return isPresent;
        })();
      `;

      console.log('🔍 [RatingReminder] Verifying injection state...');
      await InAppBrowser.executeScript({ code: checkScript });

      // If we can't find the popup but think it's injected, reset the flag
      // Note: We can't get the return value from executeScript, so we'll use a different approach

    } catch (error) {
      console.warn('🔍 [RatingReminder] Could not verify injection state, assuming not injected:', error);
      this.isInjected = false;
    }
  }

  /**
   * Force reset injection state (for debugging)
   */
  async forceResetInjectionState(): Promise<void> {
    console.log('🔍 [RatingReminder] Force resetting injection state...');
    this.isInjected = false;
    this.lastInjectionTime = 0;

    // Try to remove any existing popup
    await this.removeReminder();

    console.log('🔍 [RatingReminder] Injection state reset complete');
  }

  /**
   * Generate the complete reminder script with HTML, CSS, and JavaScript
   */
  private generateReminderScript(): string {
    const { position, zIndex } = this.config;

    // Determine positioning styles based on config
    const positionStyles = this.getPositionStyles(position);

    return `
      (function() {
        console.log('🔍 [RatingReminder] Popup script starting execution...');

        // Only inject reminder once
        if (window._ratingReminderInjected) {
          console.log('🔍 [RatingReminder] Popup already injected, skipping');
          return;
        }
        window._ratingReminderInjected = true;

        console.log('🔍 [RatingReminder] Popup script executing, creating HTML...');

        // Create reminder HTML structure
        const reminderHTML = \`
          <div id="rating-reminder-overlay" style="
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: ${zIndex};
            display: flex;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(4px);
            opacity: 0;
            transition: opacity 0.3s ease;
          ">
            <div id="rating-reminder-container" style="
              ${positionStyles}
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              border: none;
              border-radius: 16px;
              padding: 24px;
              box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
              color: white;
              max-width: 320px;
              width: 90%;
              text-align: center;
              transform: scale(0.8);
              transition: transform 0.3s ease;
            ">
              <!-- Header -->
              <div style="
                margin-bottom: 16px;
              ">
                <div style="
                  font-size: 32px;
                  margin-bottom: 8px;
                ">⭐</div>
                <h3 style="
                  margin: 0;
                  font-size: 20px;
                  font-weight: 600;
                  line-height: 1.3;
                ">Enjoying the app?</h3>
              </div>

              <!-- Message -->
              <div style="
                margin-bottom: 24px;
                font-size: 16px;
                line-height: 1.4;
                opacity: 0.9;
              ">
                Help us improve by rating the app in the store. It only takes a moment!
              </div>

              <!-- Buttons -->
              <div style="
                display: flex;
                flex-direction: column;
                gap: 12px;
              ">
                <button id="rating-reminder-rate-now" style="
                  background: rgba(255, 255, 255, 0.2);
                  border: 2px solid rgba(255, 255, 255, 0.3);
                  border-radius: 8px;
                  color: white;
                  font-size: 16px;
                  font-weight: 600;
                  padding: 12px 20px;
                  cursor: pointer;
                  transition: all 0.2s ease;
                  backdrop-filter: blur(10px);
                " onmouseover="this.style.background='rgba(255, 255, 255, 0.3)'"
                   onmouseout="this.style.background='rgba(255, 255, 255, 0.2)'">
                  ⭐ Rate Now
                </button>

                <div style="
                  display: flex;
                  gap: 8px;
                ">
                  <button id="rating-reminder-maybe-later" style="
                    flex: 1;
                    background: transparent;
                    border: 1px solid rgba(255, 255, 255, 0.3);
                    border-radius: 6px;
                    color: rgba(255, 255, 255, 0.8);
                    font-size: 14px;
                    padding: 10px 16px;
                    cursor: pointer;
                    transition: all 0.2s ease;
                  " onmouseover="this.style.background='rgba(255, 255, 255, 0.1)'"
                     onmouseout="this.style.background='transparent'">
                    Maybe Later
                  </button>

                  <button id="rating-reminder-dont-ask" style="
                    flex: 1;
                    background: transparent;
                    border: 1px solid rgba(255, 255, 255, 0.3);
                    border-radius: 6px;
                    color: rgba(255, 255, 255, 0.8);
                    font-size: 14px;
                    padding: 10px 16px;
                    cursor: pointer;
                    transition: all 0.2s ease;
                  " onmouseover="this.style.background='rgba(255, 255, 255, 0.1)'"
                     onmouseout="this.style.background='transparent'">
                    Don't Ask Again
                  </button>
                </div>
              </div>

              <!-- Close button -->
              <button id="rating-reminder-close" style="
                position: absolute;
                top: 12px;
                right: 12px;
                background: transparent;
                border: none;
                color: rgba(255, 255, 255, 0.7);
                font-size: 20px;
                cursor: pointer;
                width: 24px;
                height: 24px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 50%;
                transition: all 0.2s ease;
              " onmouseover="this.style.background='rgba(255, 255, 255, 0.2)'"
                 onmouseout="this.style.background='transparent'">
                ×
              </button>
            </div>
          </div>
        \`;

        // Inject reminder into DOM
        console.log('🔍 [RatingReminder] Injecting HTML into DOM...');
        console.log('🔍 [RatingReminder] HTML length:', reminderHTML.length);
        document.body.insertAdjacentHTML('beforeend', reminderHTML);
        console.log('🔍 [RatingReminder] HTML injected successfully');

        // Verify injection
        const injectedOverlay = document.getElementById('rating-reminder-overlay');
        console.log('🔍 [RatingReminder] Overlay found after injection:', !!injectedOverlay);

        // Get elements
        console.log('🔍 [RatingReminder] Getting DOM elements...');
        const overlay = document.getElementById('rating-reminder-overlay');
        const container = document.getElementById('rating-reminder-container');
        const rateNowBtn = document.getElementById('rating-reminder-rate-now');
        const maybeLaterBtn = document.getElementById('rating-reminder-maybe-later');
        const dontAskBtn = document.getElementById('rating-reminder-dont-ask');
        const closeBtn = document.getElementById('rating-reminder-close');

        console.log('🔍 [RatingReminder] Elements found:', {
          overlay: !!overlay,
          container: !!container,
          rateNowBtn: !!rateNowBtn,
          maybeLaterBtn: !!maybeLaterBtn,
          dontAskBtn: !!dontAskBtn,
          closeBtn: !!closeBtn
        });

        // Animate in
        console.log('🔍 [RatingReminder] Starting animation...');
        setTimeout(() => {
          console.log('🔍 [RatingReminder] Applying animation styles...');
          if (overlay) overlay.style.opacity = '1';
          if (container) container.style.transform = 'scale(1)';
          console.log('🔍 [RatingReminder] Animation applied');
        }, 10);

        // Event handlers
        function closeReminder() {
          overlay.style.opacity = '0';
          container.style.transform = 'scale(0.8)';
          setTimeout(() => {
            if (overlay && overlay.parentNode) {
              overlay.parentNode.removeChild(overlay);
            }
            if (window._ratingReminderInjected) {
              delete window._ratingReminderInjected;
            }
          }, 300);
        }

        function sendMessage(type, data = {}) {
          console.log('🔍 [RatingReminder] Sending message:', type, data);

          const message = {
            type: 'ratingReminder',
            action: type,
            data: data,
            timestamp: Date.now()
          };

          if (window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.bridge) {
            console.log('🔍 [RatingReminder] Sending via webkit messageHandlers');
            window.webkit.messageHandlers.bridge.postMessage(message);
          } else if (window.parent) {
            console.log('🔍 [RatingReminder] Sending via window.parent.postMessage');
            window.parent.postMessage(message, '*');
          } else {
            console.log('🔍 [RatingReminder] No message handler available');
          }
        }

        // Button event listeners
        console.log('🔍 [RatingReminder] Setting up button event listeners...');

        if (rateNowBtn) {
          rateNowBtn.addEventListener('click', () => {
            console.log('🔍 [RatingReminder] Rate Now button clicked');
            sendMessage('rateNow');
            closeReminder();
          });
        }

        if (maybeLaterBtn) {
          maybeLaterBtn.addEventListener('click', () => {
            console.log('🔍 [RatingReminder] Maybe Later button clicked');
            sendMessage('maybeLater');
            closeReminder();
          });
        }

        if (dontAskBtn) {
          dontAskBtn.addEventListener('click', () => {
            console.log('🔍 [RatingReminder] Don\'t Ask Again button clicked');
            sendMessage('dontAskAgain');
            closeReminder();
          });
        }

        if (closeBtn) {
          closeBtn.addEventListener('click', () => {
            console.log('🔍 [RatingReminder] Close button clicked');
            sendMessage('dismiss');
            closeReminder();
          });
        }

        // Close on overlay click
        overlay.addEventListener('click', (e) => {
          if (e.target === overlay) {
            sendMessage('dismiss');
            closeReminder();
          }
        });

        // Close on escape key
        document.addEventListener('keydown', (e) => {
          if (e.key === 'Escape') {
            sendMessage('dismiss');
            closeReminder();
          }
        });

        console.log('✅ [RatingReminder] Popup setup complete and ready!');

        // Send verification message that popup is ready
        sendMessage('popupReady', { timestamp: Date.now() });
      })();
    `;
  }

  /**
   * Get positioning styles based on configuration
   */
  private getPositionStyles(position: string): string {
    switch (position) {
      case 'top':
        return `
          position: relative;
          margin-top: 20px;
        `;
      case 'bottom':
        return `
          position: relative;
          margin-bottom: 20px;
        `;
      case 'center':
      default:
        return `
          position: relative;
        `;
    }
  }
}
