# Example environment configuration for SVA Web2App

# Core Application Settings
VITE_APP_NAME=SVA Web2App
VITE_TARGET_URL=https://sva.de
VITE_ENABLE_SCREENSHOTS=true

# State Persistence Configuration
VITE_PERSISTENCE_ENABLED=true
VITE_PERSISTENCE_MAX_AGE_DAYS=30
VITE_PERSISTENCE_MAX_SIZE_MB=5
VITE_PERSISTENCE_EXCLUDE_PATTERNS=password,token,secret,sessionId

# Debug and Development Settings
VITE_ENABLE_DEBUG_TOOLBAR=false

# Rating Reminder Configuration
VITE_RATING_REMINDER_ENABLED=true
VITE_RATING_REMINDER_THRESHOLD=10
VITE_RATING_REMINDER_FREQUENCY=7
