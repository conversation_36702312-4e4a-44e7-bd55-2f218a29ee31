# Rating Reminder Debug Guide

## Quick Testing Steps

1. **Set Low Threshold for Testing**
   - The `.env` file is already configured with `VITE_RATING_REMINDER_THRESHOLD=2`
   - This means the popup should appear after 2 app opens

2. **Build and Run the App**
   ```bash
   npm run build
   npx cap sync
   npx cap run ios  # or android
   ```

3. **Monitor Console Logs**
   - Open browser dev tools or check device logs
   - Look for logs prefixed with `🔍 [RatingReminder]`

4. **Test App Opens**
   - Open the app 2 times to reach the threshold
   - The popup should appear 5 seconds after the second open

## Console Testing Commands

When running in development mode, these global functions are available:

```javascript
// Test the rating reminder immediately
testRatingReminder()

// Reset all state for fresh testing
resetRatingReminder()

// Check current status
ratingReminderStatus()
```

## Expected Log Flow

When working correctly, you should see this sequence:

```
🔍 [RatingReminder] Initializing service...
🔍 [RatingReminder] Loading configuration...
🔍 [RatingReminder] Loading state...
✅ [RatingReminder] Service initialized successfully
🔍 [RatingReminder] trackAppOpen() called
🔍 [RatingReminder] Incrementing app open count: 0 -> 1
🔍 [RatingReminder] App open tracked successfully. Count: 1/2
🔍 [RatingReminder] Conditions not met. Blocking reasons: ["threshold not met (1 more opens needed)"]

// After second app open:
🔍 [RatingReminder] trackAppOpen() called
🔍 [RatingReminder] Incrementing app open count: 1 -> 2
🎉 [RatingReminder] Threshold reached for the first time!
🔍 [RatingReminder] shouldShowReminder() returned: true
🎯 [RatingReminder] All conditions met! Scheduling display...
🔍 [RatingReminder] Scheduling rating reminder display with 5000ms delay...
🔍 [RatingReminder] setTimeout scheduled successfully

// After 5 seconds:
🔍 [RatingReminder] 5-second delay completed, calling showReminderIfReady()
🔍 [RatingReminder] showReminderIfReady() called
🔍 [RatingReminder] Page is stable, attempting injection...
🔍 [RatingReminder] Starting injection process...
🔍 [RatingReminder] Popup script starting execution...
✅ [RatingReminder] Popup setup complete and ready!
```

## Troubleshooting

### Issue 1: No logs appearing
- Check if `VITE_RATING_REMINDER_ENABLED=true` in `.env`
- Verify the service is being imported and initialized

### Issue 2: Threshold not reached
- Check current app open count with `ratingReminderStatus()`
- Use `testRatingReminder()` to force trigger

### Issue 3: Conditions not met
- Check blocking reasons in the logs
- Verify user hasn't chosen "Don't Ask Again"
- Reset state with `resetRatingReminder()`

### Issue 4: Popup not appearing
- Check if injection logs appear
- Look for JavaScript errors in console
- Verify InAppBrowser.executeScript is working

### Issue 5: Message handling not working
- Check if message listeners are set up
- Look for message sending/receiving logs
- Test button clicks and check console

## Manual Testing Scenarios

1. **Fresh Install Test**
   ```javascript
   resetRatingReminder()
   // Then open app twice
   ```

2. **Maybe Later Test**
   ```javascript
   testRatingReminder()
   // Click "Maybe Later"
   // Wait for frequency period or adjust VITE_RATING_REMINDER_FREQUENCY
   ```

3. **Don't Ask Again Test**
   ```javascript
   testRatingReminder()
   // Click "Don't Ask Again"
   // Verify no more popups appear
   ```

## Configuration for Testing

Current test configuration in `.env`:
```
VITE_RATING_REMINDER_ENABLED=true
VITE_RATING_REMINDER_THRESHOLD=2
VITE_RATING_REMINDER_FREQUENCY=1
```

This setup allows for quick testing with minimal app opens and short frequency intervals.
