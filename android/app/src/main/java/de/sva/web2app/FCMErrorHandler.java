package de.sva.web2app;

import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

/**
 * Comprehensive error handling and logging utility for FCM operations
 * Provides structured logging, error tracking, and debugging capabilities
 */
public class FCMErrorHandler {

    private static final String TAG = "FCMErrorHandler";
    private static final String PREFS_NAME = "fcm_error_prefs";
    private static final String KEY_ERROR_COUNT = "error_count";
    private static final String KEY_LAST_ERROR = "last_error";
    private static final String KEY_LAST_ERROR_TIME = "last_error_time";

    private final Context context;
    private final SharedPreferences sharedPreferences;

    public FCMErrorHandler(Context context) {
        this.context = context;
        this.sharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
    }

    /**
     * Error types for categorization
     */
    public enum ErrorType {
        TOKEN_RETRIEVAL_FAILED("Token Retrieval Failed"),
        TOKEN_REFRESH_FAILED("Token Refresh Failed"),
        NOTIFICATION_DISPLAY_FAILED("Notification Display Failed"),
        MESSAGE_PROCESSING_FAILED("Message Processing Failed"),
        NETWORK_ERROR("Network Error"),
        PERMISSION_DENIED("Permission Denied"),
        SERVICE_UNAVAILABLE("Service Unavailable"),
        UNKNOWN_ERROR("Unknown Error");

        private final String description;

        ErrorType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * Log levels for different types of messages
     */
    public enum LogLevel {
        DEBUG, INFO, WARNING, ERROR, CRITICAL
    }

    /**
     * Log a message with specified level
     */
    public void log(LogLevel level, String message) {
        log(level, message, null);
    }

    /**
     * Log a message with exception
     */
    public void log(LogLevel level, String message, Throwable throwable) {
        String timestamp = getCurrentTimestamp();
        String logMessage = String.format("[%s] %s", timestamp, message);

        switch (level) {
            case DEBUG:
                Log.d(TAG, logMessage, throwable);
                break;
            case INFO:
                Log.i(TAG, logMessage, throwable);
                break;
            case WARNING:
                Log.w(TAG, logMessage, throwable);
                break;
            case ERROR:
                Log.e(TAG, logMessage, throwable);
                recordError(message, throwable);
                break;
            case CRITICAL:
                Log.e(TAG, "CRITICAL: " + logMessage, throwable);
                recordError("CRITICAL: " + message, throwable);
                break;
        }
    }

    /**
     * Handle FCM-specific errors
     */
    public void handleFCMError(ErrorType errorType, String details, Throwable throwable) {
        String errorMessage = String.format("FCM Error [%s]: %s", errorType.getDescription(), details);
        log(LogLevel.ERROR, errorMessage, throwable);

        // Additional error-specific handling
        switch (errorType) {
            case TOKEN_RETRIEVAL_FAILED:
                handleTokenRetrievalError(details, throwable);
                break;
            case TOKEN_REFRESH_FAILED:
                handleTokenRefreshError(details, throwable);
                break;
            case NOTIFICATION_DISPLAY_FAILED:
                handleNotificationDisplayError(details, throwable);
                break;
            case PERMISSION_DENIED:
                handlePermissionError(details, throwable);
                break;
            case NETWORK_ERROR:
                handleNetworkError(details, throwable);
                break;
            default:
                handleGenericError(errorType, details, throwable);
                break;
        }
    }

    /**
     * Handle token retrieval errors
     */
    private void handleTokenRetrievalError(String details, Throwable throwable) {
        log(LogLevel.WARNING, "Token retrieval failed, will retry later");
        
        // Could implement retry logic here
        // scheduleTokenRetrieval();
    }

    /**
     * Handle token refresh errors
     */
    private void handleTokenRefreshError(String details, Throwable throwable) {
        log(LogLevel.WARNING, "Token refresh failed, marking for retry");
        
        // Mark token as needing refresh
        SharedPreferences.Editor editor = sharedPreferences.edit();
        editor.putBoolean("token_needs_refresh", true);
        editor.apply();
    }

    /**
     * Handle notification display errors
     */
    private void handleNotificationDisplayError(String details, Throwable throwable) {
        log(LogLevel.ERROR, "Failed to display notification: " + details);
    }

    /**
     * Handle permission errors
     */
    private void handlePermissionError(String details, Throwable throwable) {
        log(LogLevel.WARNING, "Permission denied: " + details);
    }

    /**
     * Handle network errors
     */
    private void handleNetworkError(String details, Throwable throwable) {
        log(LogLevel.WARNING, "Network error: " + details);
    }

    /**
     * Handle generic errors
     */
    private void handleGenericError(ErrorType errorType, String details, Throwable throwable) {
        log(LogLevel.ERROR, String.format("Generic error [%s]: %s", errorType.getDescription(), details));
    }

    /**
     * Record error for tracking
     */
    private void recordError(String message, Throwable throwable) {
        SharedPreferences.Editor editor = sharedPreferences.edit();
        
        // Increment error count
        int errorCount = sharedPreferences.getInt(KEY_ERROR_COUNT, 0);
        editor.putInt(KEY_ERROR_COUNT, errorCount + 1);
        
        // Store last error details
        String errorDetails = message;
        if (throwable != null) {
            errorDetails += "\n" + getStackTrace(throwable);
        }
        editor.putString(KEY_LAST_ERROR, errorDetails);
        editor.putLong(KEY_LAST_ERROR_TIME, System.currentTimeMillis());
        
        editor.apply();
    }

    /**
     * Get stack trace as string
     */
    private String getStackTrace(Throwable throwable) {
        StringWriter sw = new StringWriter();
        PrintWriter pw = new PrintWriter(sw);
        throwable.printStackTrace(pw);
        return sw.toString();
    }

    /**
     * Get current timestamp
     */
    private String getCurrentTimestamp() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS", Locale.getDefault());
        return sdf.format(new Date());
    }

    /**
     * Get error statistics
     */
    public int getErrorCount() {
        return sharedPreferences.getInt(KEY_ERROR_COUNT, 0);
    }

    /**
     * Get last error details
     */
    public String getLastError() {
        return sharedPreferences.getString(KEY_LAST_ERROR, "No errors recorded");
    }

    /**
     * Get last error time
     */
    public long getLastErrorTime() {
        return sharedPreferences.getLong(KEY_LAST_ERROR_TIME, 0);
    }

    /**
     * Clear error statistics
     */
    public void clearErrorStats() {
        SharedPreferences.Editor editor = sharedPreferences.edit();
        editor.remove(KEY_ERROR_COUNT);
        editor.remove(KEY_LAST_ERROR);
        editor.remove(KEY_LAST_ERROR_TIME);
        editor.apply();
        log(LogLevel.INFO, "Error statistics cleared");
    }

    /**
     * Check if error rate is too high
     */
    public boolean isErrorRateTooHigh() {
        int errorCount = getErrorCount();
        long lastErrorTime = getLastErrorTime();
        long currentTime = System.currentTimeMillis();
        
        // Consider error rate too high if more than 10 errors in the last hour
        return errorCount > 10 && (currentTime - lastErrorTime) < 3600000; // 1 hour
    }

    /**
     * Log FCM operation start
     */
    public void logOperationStart(String operation) {
        log(LogLevel.DEBUG, "Starting FCM operation: " + operation);
    }

    /**
     * Log FCM operation success
     */
    public void logOperationSuccess(String operation) {
        log(LogLevel.INFO, "FCM operation completed successfully: " + operation);
    }

    /**
     * Log FCM operation failure
     */
    public void logOperationFailure(String operation, Throwable throwable) {
        handleFCMError(ErrorType.UNKNOWN_ERROR, "Operation failed: " + operation, throwable);
    }

    /**
     * Create error report for debugging
     */
    public String createErrorReport() {
        StringBuilder report = new StringBuilder();
        report.append("=== FCM Error Report ===\n");
        report.append("Timestamp: ").append(getCurrentTimestamp()).append("\n");
        report.append("Total Errors: ").append(getErrorCount()).append("\n");
        report.append("Last Error Time: ").append(new Date(getLastErrorTime())).append("\n");
        report.append("Last Error Details:\n").append(getLastError()).append("\n");
        report.append("Error Rate Too High: ").append(isErrorRateTooHigh()).append("\n");
        report.append("========================\n");
        
        return report.toString();
    }
}
